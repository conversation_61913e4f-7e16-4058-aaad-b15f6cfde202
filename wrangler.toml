name = "grap-v2"
main = ".output/server/index.mjs"
compatibility_date = "2024-09-19"
compatibility_flags = ["nodejs_compat"]

[build]
command = "npm run build"

# 환경 변수 (개발용 - 프로덕션에서는 Cloudflare 대시보드에서 설정)
[vars]
# 데이터베이스 설정은 Cloudflare 대시보드에서 설정하세요
# DB_HOST = ""
# DB_USER = ""
# DB_PASSWORD = ""
# DB_DATABASE = ""
# DB_PORT = ""

# Supabase 설정은 Cloudflare 대시보드에서 설정하세요
# SUPABASE_URL = ""
# SUPABASE_ANON_KEY = ""
# SUPABASE_SERVICE_ROLE_KEY = ""

# JWT 설정은 Cloudflare 대시보드에서 설정하세요
# JWT_SECRET_KEY = ""
# JWT_EXPIRES_IN = "24h"

# Kakao Map API Key는 Cloudflare 대시보드에서 설정하세요
# KAKAO_MAP_API_KEY = ""

# Hyperdrive URL (Cloudflare D1 사용시)
# HYPERDRIVE_URL = ""

# 크론 작업 설정 (필요시)
# [[triggers.crons]]
# cron = "0 2 * * *"  # 매일 오전 2시 (주유소 API)

# [[triggers.crons]]
# cron = "0 9 * * *"  # 매일 오전 9시 (기타 API)

# KV 네임스페이스 (필요시)
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your-kv-namespace-id"

# D1 데이터베이스 (필요시)
# [[d1_databases]]
# binding = "DB"
# database_name = "grap-database"
# database_id = "your-d1-database-id"

# R2 버킷 (파일 저장용, 필요시)
# [[r2_buckets]]
# binding = "ASSETS"
# bucket_name = "grap-assets"
